using Duckie.Shared.Views.Common;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace Duckie.Views.Controls;

public partial class NavigationHoverCard : UserControl
{
    private readonly ObservableCollection<NavigationMenuItem> _items = new();
    private Storyboard _fadeInAnimation;
    private Storyboard _fadeOutAnimation;
    private bool _isMouseOver = false;

    public event EventHandler CardMouseEnter;
    public event EventHandler CardMouseLeave;

    public NavigationHoverCard()
    {
        InitializeComponent();
        InitializeAnimations();
    }

    public ObservableCollection<NavigationMenuItem> Items => _items;

    public string HeaderTextValue
    {
        get => HeaderText.Text;
        set => HeaderText.Text = value;
    }

    public IconType HeaderIconValue
    {
        get => HeaderIcon.IconType;
        set => HeaderIcon.IconType = value;
    }

    private void InitializeAnimations()
    {
        _fadeInAnimation = (Storyboard)Resources["FadeInAnimation"];
        _fadeOutAnimation = (Storyboard)Resources["FadeOutAnimation"];
        
        _fadeOutAnimation.Completed += (s, e) => Visibility = Visibility.Collapsed;
    }

    public void SetHeaderInfo(string text, IconType iconType)
    {
        HeaderText.Text = text;
        HeaderIcon.IconType = iconType;
    }

    public void AddItem(NavigationMenuItem item)
    {
        // Create a simplified version for the hover card
        var hoverItem = new Button
        {
            HorizontalAlignment = HorizontalAlignment.Stretch,
            HorizontalContentAlignment = HorizontalAlignment.Left,
            Background = System.Windows.Media.Brushes.Transparent,
            BorderThickness = new Thickness(0),
            Padding = new Thickness(8, 6, 8, 6),
            Margin = new Thickness(0, 1, 0, 1),
            Cursor = System.Windows.Input.Cursors.Hand
        };

        // Create content
        var stackPanel = new StackPanel { Orientation = Orientation.Horizontal };
        
        var icon = new IconEx
        {
            IconType = item.IconType,
            Width = 14,
            Height = 14,
            Margin = new Thickness(0, 0, 8, 0)
        };

        var textBlock = new TextBlock
        {
            Text = item.Text,
            FontSize = 12,
            Foreground = System.Windows.Media.Brushes.Black,
            VerticalAlignment = VerticalAlignment.Center
        };

        stackPanel.Children.Add(icon);
        stackPanel.Children.Add(textBlock);
        hoverItem.Content = stackPanel;

        // Handle click event
        hoverItem.Click += (s, e) =>
        {
            // Trigger the navigation item's click event
            item.TriggerClick();
            Hide();
        };

        // Handle hover effects
        hoverItem.MouseEnter += (s, e) => hoverItem.Background = System.Windows.Media.Brushes.LightGray;
        hoverItem.MouseLeave += (s, e) => hoverItem.Background = System.Windows.Media.Brushes.Transparent;

        ItemsContainer.Children.Add(hoverItem);
        _items.Add(item);
    }

    public void ClearItems()
    {
        ItemsContainer.Children.Clear();
        _items.Clear();
    }

    public void Show()
    {
        if (Visibility == Visibility.Visible) return;

        Visibility = Visibility.Visible;
        _fadeOutAnimation.Stop();
        _fadeInAnimation.Begin();
    }

    public void Hide()
    {
        if (Visibility == Visibility.Collapsed) return;

        _fadeInAnimation.Stop();
        _fadeOutAnimation.Begin();
    }

    private void CardBorder_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
    {
        _isMouseOver = true;
        CardMouseEnter?.Invoke(this, EventArgs.Empty);
    }

    private void CardBorder_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
    {
        _isMouseOver = false;
        CardMouseLeave?.Invoke(this, EventArgs.Empty);
    }

    public bool IsMouseOverCard => _isMouseOver;
}
