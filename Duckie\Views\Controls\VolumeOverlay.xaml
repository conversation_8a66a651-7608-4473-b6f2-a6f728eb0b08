<UserControl x:Class="Duckie.Views.Controls.VolumeOverlay"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="190">
    <UserControl.Resources>
        <Storyboard x:Key="FadeOutAnimation" Completed="FadeOutAnimation_Completed" FillBehavior="Stop">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="1" To="0" Duration="0:0:0.2"/>
        </Storyboard>
    </UserControl.Resources>

    <Border Background="#F222" CornerRadius="4" Padding="10" MinWidth="100" MaxWidth="200">
        <Border.Effect>
            <DropShadowEffect Color="Black" BlurRadius="20" ShadowDepth="2" Opacity="0.3"/>
        </Border.Effect>

        <StackPanel Orientation="Horizontal">
            <common:IconEx x:Name="volumeIcon" IconType="SpeakerLow" IconSize="16" IconColor="White" />
            <Grid Margin="8 0" Width="100">
                <Rectangle x:Name="volumeBackgroundBar" Fill="#33FFFFFF" Height="3" RadiusX="3" RadiusY="3"/>
                <Rectangle x:Name="volumeBar" Width="50" Fill="White" Height="3" RadiusX="3" RadiusY="3" HorizontalAlignment="Left"/>
            </Grid>
            <TextBlock x:Name="VolumePercentText" Text="100%" Foreground="White" FontSize="14" VerticalAlignment="Center"/>
        </StackPanel>
    </Border>
</UserControl>
