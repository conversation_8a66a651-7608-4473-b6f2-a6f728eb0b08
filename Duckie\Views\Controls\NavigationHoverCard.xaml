<UserControl x:Class="Duckie.Views.Controls.NavigationHoverCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="180"
             IsHitTestVisible="True">
    
    <UserControl.Resources>
        <!-- Fade in animation with easing -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                             From="0" To="1" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                             From="0.9" To="1" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                             From="0.9" To="1" Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Fade out animation -->
        <Storyboard x:Key="FadeOutAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                             From="1" To="0" Duration="0:0:0.15">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                             From="1" To="0.95" Duration="0:0:0.15">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                             From="1" To="0.95" Duration="0:0:0.15">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>
    
    <UserControl.RenderTransform>
        <ScaleTransform ScaleX="1" ScaleY="1"/>
    </UserControl.RenderTransform>
    
    <!-- Main card container -->
    <Border x:Name="CardBorder" 
            Background="White" 
            BorderBrush="#E9ECEF" 
            BorderThickness="1" 
            CornerRadius="8" 
            Padding="8"
            Opacity="0"
            MouseEnter="CardBorder_MouseEnter"
            MouseLeave="CardBorder_MouseLeave">
        <Border.Effect>
            <DropShadowEffect Color="Black" BlurRadius="12" ShadowDepth="2" Opacity="0.15"/>
        </Border.Effect>
        
        <StackPanel>
            <!-- Card header -->
            <Grid Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <common:IconEx x:Name="HeaderIcon" Grid.Column="0" 
                               Width="16" Height="16" Margin="0,0,8,0"/>
                <TextBlock x:Name="HeaderText" Grid.Column="1" 
                           FontWeight="SemiBold" FontSize="12" 
                           Foreground="#495057" VerticalAlignment="Center"/>
            </Grid>
            
            <!-- Separator -->
            <Rectangle Height="1" Fill="#E9ECEF" Margin="0,0,0,8"/>
            
            <!-- Items container -->
            <StackPanel x:Name="ItemsContainer">
                <!-- Navigation items will be added here programmatically -->
            </StackPanel>
        </StackPanel>
    </Border>
</UserControl>
