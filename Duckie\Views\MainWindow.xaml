﻿<Window x:Class="Duckie.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Duckie.Views"
        xmlns:controls="clr-namespace:Duckie.Views.Controls"
        xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
        xmlns:loc="clr-namespace:Duckie.Shared.Utils.Localization;assembly=Duckie.Shared"
        xmlns:imageViews="clr-namespace:Duckie.Views;assembly=Duckie.Image"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Closing="Window_Closing"
        Visibility="Hidden"
        Title="{loc:Localize AppTitle}" Height="420" Width="760">
    <!-- 主内容区域 -->
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="SidebarColumn" Width="120"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Left sidebar navigation -->
        <Border Grid.Column="0" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,0,1,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Margin="14,8,0,0" HorizontalAlignment="Left">
                    <common:IconButton IconType="AlignJustify" Click="ToggleButton_Click" />
                </StackPanel>
                <!-- Main navigation items -->
                <StackPanel Grid.Row="1" Margin="0,8,0,0">
                    <!-- Image Processing -->
                    <controls:NavigationMenuItem x:Name="NavImageProcessing" IconType="Image" Text="{loc:Localize Nav_Image}" Click="MenuImageProcessing_Click"/>

                    <!-- PAC Management -->
                    <controls:NavigationMenuItem x:Name="NavPacManagement" IconType="Network" Text="{loc:Localize Nav_PAC}" Click="MenuPacManagement_Click"/>
                </StackPanel>

                <!-- Bottom navigation items -->
                <StackPanel Grid.Row="2" Margin="0,16,0,16">
                    <!-- Settings -->
                    <controls:NavigationMenuItem x:Name="NavSettings" IconType="SettingLine" Text="{loc:Localize Nav_Settings}" Click="MenuSettings_Click"/>

                    <!-- About -->
                    <controls:NavigationMenuItem x:Name="NavAbout" IconType="InformationLine" Text="{loc:Localize Nav_About}" Click="MenuAbout_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main content area -->
        <Grid Grid.Column="1">
            <!-- Image processing area -->
            <imageViews:ImageView x:Name="ImageViewControl" Margin="10" Visibility="Visible"/>

            <!-- PAC management area -->
            <local:PacManageView x:Name="PacManageViewControl" Margin="10" Visibility="Collapsed"/>

            <!-- Settings area -->
            <local:SettingsView x:Name="SettingsViewControl" Margin="10" Visibility="Collapsed"/>

            <!-- About area -->
            <local:AboutView x:Name="AboutViewControl" Margin="10" Visibility="Collapsed"/>
        </Grid>
    </Grid>
</Window>
