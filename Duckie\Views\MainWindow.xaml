﻿<Window x:Class="Duckie.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Duckie.Views"
        xmlns:controls="clr-namespace:Duckie.Views.Controls"
        xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
        xmlns:loc="clr-namespace:Duckie.Shared.Utils.Localization;assembly=Duckie.Shared"
        xmlns:imageViews="clr-namespace:Duckie.Views;assembly=Duckie.Image"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Title="{loc:Localize AppTitle}" Height="420" Width="760">
    <!-- 主内容区域 -->
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="SidebarColumn" Width="160"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Left sidebar navigation -->
        <Border Grid.Column="0" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,0,1,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Margin="14,8,0,0" HorizontalAlignment="Left">
                    <common:IconButton IconType="AlignJustify" Click="ToggleButton_Click" />
                </StackPanel>
                <!-- Main navigation items -->
                <StackPanel Grid.Row="1" Margin="0,8,0,0">
                    <!-- Media Tools Group -->
                    <controls:NavigationGroup x:Name="NavGroupMedia" IconType="Image" Text="{loc:Localize NavGroup_Media}" IsExpanded="True"/>

                    <!-- Network Tools Group -->
                    <controls:NavigationGroup x:Name="NavGroupNetwork" IconType="Network" Text="{loc:Localize NavGroup_Network}" IsExpanded="True"/>

                    <!-- System Tools Group -->
                    <controls:NavigationGroup x:Name="NavGroupSystem" IconType="SettingLine" Text="{loc:Localize NavGroup_System}" IsExpanded="True"/>
                </StackPanel>

                <!-- Bottom navigation items -->
                <StackPanel Grid.Row="2" Margin="0,16,0,16" x:Name="BottomNavigationPanel">
                    <!-- Settings and About will be added programmatically -->
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main content area -->
        <Grid Grid.Column="1">
            <!-- Image processing area -->
            <imageViews:ImageView x:Name="ImageViewControl" Margin="10" Visibility="Visible"/>

            <!-- PAC management area -->
            <local:PacManageView x:Name="PacManageViewControl" Margin="10" Visibility="Collapsed"/>

            <!-- Volume control area -->
            <local:VolumeControlView x:Name="VolumeControlViewControl" Margin="10" Visibility="Collapsed"/>

            <!-- Hotkey management area -->
            <local:HotkeyManagementView x:Name="HotkeyManagementViewControl" Margin="10" Visibility="Collapsed"/>

            <!-- Settings area -->
            <local:SettingsView x:Name="SettingsViewControl" Margin="10" Visibility="Collapsed"/>

            <!-- About area -->
            <local:AboutView x:Name="AboutViewControl" Margin="10" Visibility="Collapsed"/>
        </Grid>
    </Grid>
</Window>
