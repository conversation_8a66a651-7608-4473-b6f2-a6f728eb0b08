<Window x:Class="Duckie.Shared.Views.Common.DialogEx"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Dialog" Height="300" Width="400"
        ResizeMode="NoResize" WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 内容区域 -->
        <ContentPresenter x:Name="ContentArea" Grid.Row="0" Margin="0,0,0,20"/>

        <!-- 按钮区域 -->
        <StackPanel x:Name="ButtonPanel" Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
            <!-- 按钮将动态添加到这里 -->
        </StackPanel>
    </Grid>
</Window>
