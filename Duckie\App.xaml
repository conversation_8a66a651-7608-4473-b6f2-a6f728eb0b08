﻿<Application x:Class="Duckie.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:<PERSON><PERSON>"
             DispatcherUnhandledException="Application_DispatcherUnhandledException"
             StartupUri="Views/MainWindow.xaml"
             Startup="Application_Startup"
             Exit="Application_Exit">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Views/Themes.xaml" />
                <ResourceDictionary Source="Views/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
