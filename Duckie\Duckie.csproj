﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net480</TargetFramework>
    <UseWPF>true</UseWPF>
    <Version>0.4.0</Version>
    <LangVersion>Latest</LangVersion>
    <ImplicitUsings>true</ImplicitUsings>
  </PropertyGroup>

  <PropertyGroup>
    <ApplicationIcon>EmbeddedResources\duck.ico</ApplicationIcon>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="EmbeddedResources\duck.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Costura.Fody" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Duckie.Image\Duckie.Image.csproj" />
    <ProjectReference Include="..\Duckie.Shared\Duckie.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
</Project>
