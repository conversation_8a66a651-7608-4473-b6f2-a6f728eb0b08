<UserControl x:Class="Duckie.Views.Controls.NavigationMenuItem"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="200">
    
    <Button x:Name="MenuButton" 
            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
            Padding="0" Margin="0" Height="40" Cursor="Hand"
            Click="MenuButton_Click">
        <Button.Style>
            <Style TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid x:Name="BackgroundGrid" Background="{TemplateBinding Background}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="12"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <!-- Selection indicator -->
                                <Rectangle x:Name="SelectionIndicator" Grid.Column="0"
                                           RadiusX="2" RadiusY="2"
                                           Fill="#0065B8" Width="4" Margin="6 10 0 10" Visibility="Collapsed"/>
                                <!-- Content area -->
                                <Grid Grid.Column="1" Margin="8,0,0,0">
                                    <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalAlignment="Center"/>
                                </Grid>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="BackgroundGrid" Property="Background" Value="#E9ECEF"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="BackgroundGrid" Property="Background" Value="#DEE2E6"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </Button.Style>
        
        <StackPanel Orientation="Horizontal">
            <common:IconEx x:Name="Icon" Margin="0,0,12,0"/>
            <TextBlock x:Name="TextBlock" VerticalAlignment="Center" Foreground="#495057"/>
        </StackPanel>
    </Button>
</UserControl>
