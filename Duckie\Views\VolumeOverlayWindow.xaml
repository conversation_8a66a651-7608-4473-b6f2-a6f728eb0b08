<Window x:Class="Duckie.Views.VolumeOverlayWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:Duckie.Views.Controls"
        Title="VolumeOverlay" 
        Height="40" 
        Width="190"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ShowInTaskbar="False"
        Topmost="True"
        WindowStartupLocation="Manual"
        ResizeMode="NoResize"
        IsHitTestVisible="False">
    
    <controls:VolumeOverlay x:Name="VolumeOverlayControl"/>
</Window>
