﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35931.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "<PERSON><PERSON>", "<PERSON><PERSON>\Duckie.csproj", "{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}"
EndProject
Project("{C7167F0D-BC9F-4E6E-AFE1-012C56B48DB5}") = "Duckie.Pack", "Duckie.Pack\Duckie.Pack.wapproj", "{13058138-35F0-4387-9223-80D623886FF3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Duckie.Shared", "Duckie.Shared\Duckie.Shared.csproj", "{C2B7A332-25B6-4113-BA09-0E6AE6460A98}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Duckie.Image", "Duckie.Image\Duckie.Image.csproj", "{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|ARM64 = Debug|ARM64
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|ARM64 = Release|ARM64
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|ARM.Build.0 = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|ARM64.Build.0 = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|x64.Build.0 = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Debug|x86.Build.0 = Debug|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|Any CPU.Build.0 = Release|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|ARM.ActiveCfg = Release|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|ARM.Build.0 = Release|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|ARM64.ActiveCfg = Release|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|ARM64.Build.0 = Release|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|x64.ActiveCfg = Release|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|x64.Build.0 = Release|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|x86.ActiveCfg = Release|Any CPU
		{CDA78C78-260C-4C7B-B6F9-2E99DFD52812}.Release|x86.Build.0 = Release|Any CPU
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|ARM.ActiveCfg = Debug|ARM
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|ARM.Build.0 = Debug|ARM
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|ARM.Deploy.0 = Debug|ARM
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|ARM64.Build.0 = Debug|ARM64
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|ARM64.Deploy.0 = Debug|ARM64
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|x64.ActiveCfg = Debug|x64
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|x64.Build.0 = Debug|x64
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|x64.Deploy.0 = Debug|x64
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|x86.ActiveCfg = Debug|x86
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|x86.Build.0 = Debug|x86
		{13058138-35F0-4387-9223-80D623886FF3}.Debug|x86.Deploy.0 = Debug|x86
		{13058138-35F0-4387-9223-80D623886FF3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{13058138-35F0-4387-9223-80D623886FF3}.Release|Any CPU.Build.0 = Release|Any CPU
		{13058138-35F0-4387-9223-80D623886FF3}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{13058138-35F0-4387-9223-80D623886FF3}.Release|ARM.ActiveCfg = Release|ARM
		{13058138-35F0-4387-9223-80D623886FF3}.Release|ARM.Build.0 = Release|ARM
		{13058138-35F0-4387-9223-80D623886FF3}.Release|ARM.Deploy.0 = Release|ARM
		{13058138-35F0-4387-9223-80D623886FF3}.Release|ARM64.ActiveCfg = Release|ARM64
		{13058138-35F0-4387-9223-80D623886FF3}.Release|ARM64.Build.0 = Release|ARM64
		{13058138-35F0-4387-9223-80D623886FF3}.Release|ARM64.Deploy.0 = Release|ARM64
		{13058138-35F0-4387-9223-80D623886FF3}.Release|x64.ActiveCfg = Release|x64
		{13058138-35F0-4387-9223-80D623886FF3}.Release|x64.Build.0 = Release|x64
		{13058138-35F0-4387-9223-80D623886FF3}.Release|x64.Deploy.0 = Release|x64
		{13058138-35F0-4387-9223-80D623886FF3}.Release|x86.ActiveCfg = Release|x86
		{13058138-35F0-4387-9223-80D623886FF3}.Release|x86.Build.0 = Release|x86
		{13058138-35F0-4387-9223-80D623886FF3}.Release|x86.Deploy.0 = Release|x86
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|ARM.Build.0 = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|ARM64.Build.0 = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|x64.Build.0 = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Debug|x86.Build.0 = Debug|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|Any CPU.Build.0 = Release|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|ARM.ActiveCfg = Release|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|ARM.Build.0 = Release|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|ARM64.ActiveCfg = Release|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|ARM64.Build.0 = Release|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|x64.ActiveCfg = Release|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|x64.Build.0 = Release|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|x86.ActiveCfg = Release|Any CPU
		{C2B7A332-25B6-4113-BA09-0E6AE6460A98}.Release|x86.Build.0 = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|ARM.Build.0 = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|ARM64.Build.0 = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|x64.Build.0 = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Debug|x86.Build.0 = Debug|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|Any CPU.Build.0 = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|ARM.ActiveCfg = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|ARM.Build.0 = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|ARM64.ActiveCfg = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|ARM64.Build.0 = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|x64.ActiveCfg = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|x64.Build.0 = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|x86.ActiveCfg = Release|Any CPU
		{DAF6D58D-A57D-499D-AAE2-D32FBECA290D}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{13058138-35F0-4387-9223-80D623886FF3} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {84CD546D-1F0B-46D0-86E3-F5723303277B}
	EndGlobalSection
EndGlobal
