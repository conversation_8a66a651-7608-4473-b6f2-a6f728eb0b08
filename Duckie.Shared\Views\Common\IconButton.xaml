<Button x:Class="Duckie.Shared.Views.Common.IconButton"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:Duckie.Shared.Views.Common"
        Background="Transparent"
        BorderBrush="Transparent"
        BorderThickness="1"
        Width="28"
        Height="28"
        Padding="8"
        Cursor="Hand">
    <Button.Style>
        <Style TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <local:IconEx x:Name="IconElement"
                                          IconSize="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IconSize}"
                                          IconType="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IconType}"
                                          IconColor="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=IconColor}" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#F8F9FA"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="#DEE2E6"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E9ECEF"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="IconElement" Property="IconColor" Value="{StaticResource DisabledForegroundBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Button.Style>
</Button>
