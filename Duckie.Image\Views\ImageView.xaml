﻿<UserControl x:Class="Duckie.Views.ImageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Duckie.Views"
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
             xmlns:loc="clr-namespace:Duckie.Shared.Utils.Localization;assembly=Duckie.Shared"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             AllowDrop="True"
             Drop="ImageView_Drop"
             DragEnter="ImageView_DragEnter"
             DragLeave="ImageView_DragLeave"
             Focusable="True">
    <!-- 样式已移动到全局主题文件中，减少内存占用 -->

    <DockPanel>
        <!-- Modern Toolbar -->
        <Border DockPanel.Dock="Top" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,0,0,1" Padding="10,6">
            <DockPanel>
                <!-- Save Button (Right) -->
                <Button x:Name="SaveButton" Content="{loc:Localize Btn_ExportICO}" Click="ButtonSave_Click"
                        Style="{StaticResource ModernButtonStyle}" DockPanel.Dock="Right" IsEnabled="False"/>

                <!-- Separator -->
                <Rectangle DockPanel.Dock="Right" Width="1" Fill="#DEE2E6" Margin="15,0"/>

                <!-- Main Toolbar (Left) -->
                <StackPanel Orientation="Horizontal">
                    <!-- Open Button -->
                    <Button Content="{loc:Localize Btn_Open}" Click="ButtonOpen_Click"
                            Style="{StaticResource ModernButtonStyle}"/>

                    <!-- Separator -->
                    <Rectangle Width="1" Fill="#DEE2E6" Margin="15,0"/>

                    <!-- Transform Group -->
                    <StackPanel Orientation="Horizontal">
                        <!-- Rotate 270° Counter-clockwise (Left) -->
                        <common:IconButton x:Name="RotateLeftButton" Click="ButtonRotate270_Click" ToolTip="{loc:Localize Tooltip_RotateLeft}"
                                           Margin="0,0,4,0" IconType="ArrowCounterClockwise" IsEnabled="False"/>

                        <!-- Rotate 90° Clockwise (Right) -->
                        <common:IconButton x:Name="RotateRightButton" Click="ButtonRotate90_Click" ToolTip="{loc:Localize Tooltip_RotateRight}"
                                           Margin="0,0,8,0" IconType="ArrowClockwise" IsEnabled="False"/>

                        <!-- Flip Horizontal -->
                        <common:IconButton x:Name="FlipHButton" Click="ButtonFlipH_Click" ToolTip="{loc:Localize Tooltip_FlipHorizontal}"
                                           Margin="0,0,4,0" IconType="FlipHorizontalFill" IsEnabled="False"/>

                        <!-- Flip Vertical -->
                        <common:IconButton x:Name="FlipVButton" Click="ButtonFlipV_Click" ToolTip="{loc:Localize Tooltip_FlipVertical}"
                                           IconType="FlipVerticalFill" IsEnabled="False"/>
                    </StackPanel>

                    <!-- Separator -->
                    <Rectangle Width="1" Fill="#DEE2E6" Margin="15,0"/>

                    <!-- Zoom Group -->
                    <StackPanel Orientation="Horizontal">
                        <common:IconButton x:Name="ZoomOutButton" Click="ButtonZoomOut_Click" ToolTip="{loc:Localize Tooltip_ZoomOut}"
                                           Margin="0,0,4,0" IconType="ZoomOut" IsEnabled="False"/>

                        <Slider x:Name="ZoomSlider" Width="80" Minimum="0.1" Maximum="3.0" Value="1.0"
                                VerticalAlignment="Center" Margin="0,0,4,0" ValueChanged="ZoomSlider_ValueChanged" IsEnabled="False"/>

                        <common:IconButton x:Name="ZoomInButton" Click="ButtonZoomIn_Click" ToolTip="{loc:Localize Tooltip_ZoomIn}"
                                           Margin="0,0,8,0" IconType="ZoomIn" IsEnabled="False"/>

                        <common:IconButton x:Name="FitToWindowButton" Click="ButtonZoomFit_Click" ToolTip="{loc:Localize Tooltip_FitToWindow}"
                                           IconType="ExpandSolid" IsEnabled="False"/>
                    </StackPanel>
                </StackPanel>
            </DockPanel>
        </Border>
        <StatusBar DockPanel.Dock="Bottom" Height="30" Padding="6 0" Background="#EEEEF2">
            <StatusBarItem>
                <TextBlock x:Name="StatusText" Text="{loc:Localize Status_Ready}" />
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock x:Name="ImageInfoText" Text="" />
            </StatusBarItem>
        </StatusBar>
        <DockPanel>
            <Grid>
                <!-- 优化的棋盘背景 - 保持视觉效果，减少内存占用 -->
                <Rectangle>
                    <Rectangle.Fill>
                        <DrawingBrush TileMode="Tile" Viewport="0,0,20,20" ViewportUnits="Absolute">
                            <DrawingBrush.Drawing>
                                <!-- 使用更简单的实现，但保持棋盘效果 -->
                                <DrawingGroup>
                                    <GeometryDrawing Brush="#FFEEEEEE">
                                        <GeometryDrawing.Geometry>
                                            <RectangleGeometry Rect="0,0,20,20" />
                                        </GeometryDrawing.Geometry>
                                    </GeometryDrawing>
                                    <GeometryDrawing Brush="#FFDDDDDD">
                                        <GeometryDrawing.Geometry>
                                            <GeometryGroup>
                                                <RectangleGeometry Rect="0,0,10,10" />
                                                <RectangleGeometry Rect="10,10,10,10" />
                                            </GeometryGroup>
                                        </GeometryDrawing.Geometry>
                                    </GeometryDrawing>
                                </DrawingGroup>
                            </DrawingBrush.Drawing>
                        </DrawingBrush>
                    </Rectangle.Fill>
                </Rectangle>
                <common:ImageScrollViewer>
                    <Image x:Name="image" />
                </common:ImageScrollViewer>
            </Grid>
        </DockPanel>
    </DockPanel>
</UserControl>
