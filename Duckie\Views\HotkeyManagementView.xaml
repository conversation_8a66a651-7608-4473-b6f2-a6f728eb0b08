<UserControl x:Class="Duckie.Views.HotkeyManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
             xmlns:loc="clr-namespace:Duckie.Shared.Utils.Localization;assembly=Duckie.Shared"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="20">
            <!-- Header -->
            <TextBlock Text="{loc:Localize Nav_HotkeyManagement}" 
                       FontSize="24" FontWeight="Bold" 
                       Foreground="#333" Margin="0,0,0,20"/>
            
            <!-- Registered Hotkeys Section -->
            <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1" 
                    CornerRadius="8" Padding="20" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="已注册的全局热键" FontSize="16" FontWeight="SemiBold" 
                               Foreground="#495057" Margin="0,0,0,15"/>
                    
                    <!-- Hotkeys List -->
                    <ItemsControl x:Name="HotkeysItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                                        CornerRadius="6" Padding="15" Margin="0,0,0,10">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding Name}" FontWeight="SemiBold" 
                                                       Foreground="#495057" Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding Description}" FontSize="12" 
                                                       Foreground="#6C757D"/>
                                        </StackPanel>
                                        
                                        <Border Grid.Column="1" Background="White" 
                                                BorderBrush="#CED4DA" BorderThickness="1" CornerRadius="4" 
                                                Padding="8,4" Margin="10,0">
                                            <TextBlock Text="{Binding KeyCombination}" FontFamily="Consolas" 
                                                       FontSize="12" Foreground="#495057"/>
                                        </Border>
                                        
                                        <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="10,0,0,0">
                                            <Button Content="测试" Style="{StaticResource ModernButtonStyle}" 
                                                    Padding="8,4" Margin="0,0,5,0" FontSize="11"
                                                    Click="TestHotkey_Click" Tag="{Binding}"/>
                                            <Button Content="禁用" Style="{StaticResource ModernButtonStyle}" 
                                                    Padding="8,4" FontSize="11"
                                                    Click="DisableHotkey_Click" Tag="{Binding}"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                    
                    <!-- Refresh Button -->
                    <Button Content="刷新热键列表" Style="{StaticResource ModernButtonStyle}" 
                            HorizontalAlignment="Left" Margin="0,15,0,0" Padding="15,8" 
                            Click="RefreshHotkeys_Click"/>
                </StackPanel>
            </Border>
            
            <!-- Hotkey Categories Section -->
            <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1" 
                    CornerRadius="8" Padding="20" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="热键分类" FontSize="16" FontWeight="SemiBold" 
                               Foreground="#495057" Margin="0,0,0,15"/>
                    
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Application Hotkeys -->
                        <Expander Grid.Row="0" Header="应用程序热键" IsExpanded="True" Margin="0,5">
                            <StackPanel Margin="20,10,0,10">
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="显示/隐藏 Duckie" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" Background="#F8F9FA" BorderBrush="#DEE2E6" 
                                            BorderThickness="1" CornerRadius="4" Padding="8,4">
                                        <TextBlock Text="Alt + Shift + E" FontFamily="Consolas" FontSize="12"/>
                                    </Border>
                                </Grid>
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="退出 Duckie" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" Background="#F8F9FA" BorderBrush="#DEE2E6" 
                                            BorderThickness="1" CornerRadius="4" Padding="8,4">
                                        <TextBlock Text="Alt + Shift + Q" FontFamily="Consolas" FontSize="12"/>
                                    </Border>
                                </Grid>
                            </StackPanel>
                        </Expander>
                        
                        <!-- Volume Hotkeys -->
                        <Expander Grid.Row="1" Header="音量控制热键" IsExpanded="True" Margin="0,5">
                            <StackPanel Margin="20,10,0,10">
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="音量减" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" Background="#F8F9FA" BorderBrush="#DEE2E6" 
                                            BorderThickness="1" CornerRadius="4" Padding="8,4">
                                        <TextBlock Text="Alt + Shift + 8" FontFamily="Consolas" FontSize="12"/>
                                    </Border>
                                </Grid>
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="音量加" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" Background="#F8F9FA" BorderBrush="#DEE2E6" 
                                            BorderThickness="1" CornerRadius="4" Padding="8,4">
                                        <TextBlock Text="Alt + Shift + 9" FontFamily="Consolas" FontSize="12"/>
                                    </Border>
                                </Grid>
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="静音切换" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" Background="#F8F9FA" BorderBrush="#DEE2E6" 
                                            BorderThickness="1" CornerRadius="4" Padding="8,4">
                                        <TextBlock Text="Alt + Shift + 0" FontFamily="Consolas" FontSize="12"/>
                                    </Border>
                                </Grid>
                            </StackPanel>
                        </Expander>
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- Hotkey Status Section -->
            <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1" 
                    CornerRadius="8" Padding="20">
                <StackPanel>
                    <TextBlock Text="热键状态" FontSize="16" FontWeight="SemiBold" 
                               Foreground="#495057" Margin="0,0,0,15"/>
                    
                    <TextBlock x:Name="HotkeyStatusText" Text="所有热键正常工作" 
                               Foreground="#28A745" Margin="0,0,0,10"/>
                    
                    <Button Content="重新注册所有热键" Style="{StaticResource ModernButtonStyle}" 
                            HorizontalAlignment="Left" Padding="15,8" 
                            Click="ReregisterHotkeys_Click"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
