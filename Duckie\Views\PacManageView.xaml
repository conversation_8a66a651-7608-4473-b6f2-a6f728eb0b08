<UserControl x:Class="Duckie.Views.PacManageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Duckie.Views"
             xmlns:loc="clr-namespace:Duckie.Shared.Utils.Localization;assembly=Duckie.Shared"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">



    <DockPanel>
        <!-- 顶部工具栏 - 与ImageView风格一致 -->
        <Border DockPanel.Dock="Top" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,0,0,1" Padding="10, 6">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 操作按钮 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Content="{loc:Localize Btn_AddPAC}" Click="AddPacButton_Click"
                            Style="{StaticResource ModernButtonStyle}" Margin="0,0,10,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 - 与ImageView风格一致 -->
        <DockPanel>
            <StatusBar DockPanel.Dock="Bottom" Height="30" Padding="6 0" Background="#EEEEF2">
                <StatusBarItem>
                    <TextBlock x:Name="StatusText" Text="{loc:Localize Status_Ready}" />
                </StatusBarItem>
            </StatusBar>

            <DockPanel>
                <Grid>
                    <!-- PAC配置列表区域 -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="10">
                        <StackPanel x:Name="PacListPanel">
                            <!-- PAC配置项将动态添加到这里 -->
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </DockPanel>
        </DockPanel>
    </DockPanel>
</UserControl>
