using Duckie.Shared.Views.Common;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace Duckie.Views.Controls;

public partial class NavigationGroup : UserControl
{
    public static readonly DependencyProperty IconTypeProperty =
        DependencyProperty.Register(nameof(IconType), typeof(IconType), typeof(NavigationGroup),
            new PropertyMetadata(IconType.ExpandSolid, OnIconTypeChanged));

    public static readonly DependencyProperty TextProperty =
        DependencyProperty.Register(nameof(Text), typeof(string), typeof(NavigationGroup),
            new PropertyMetadata(string.Empty, OnTextChanged));

    public static readonly DependencyProperty IsExpandedProperty =
        DependencyProperty.Register(nameof(IsExpanded), typeof(bool), typeof(NavigationGroup),
            new PropertyMetadata(true, OnIsExpandedChanged));

    public static readonly DependencyProperty IsTextVisibleProperty =
        DependencyProperty.Register(nameof(IsTextVisible), typeof(bool), typeof(NavigationGroup),
            new PropertyMetadata(true, OnIsTextVisibleChanged));

    private readonly ObservableCollection<NavigationMenuItem> _items = new();

    public NavigationGroup()
    {
        InitializeComponent();
        UpdateExpandState();
    }

    public IconType IconType
    {
        get { return (IconType)GetValue(IconTypeProperty); }
        set { SetValue(IconTypeProperty, value); }
    }

    public string Text
    {
        get { return (string)GetValue(TextProperty); }
        set { SetValue(TextProperty, value); }
    }

    public bool IsExpanded
    {
        get { return (bool)GetValue(IsExpandedProperty); }
        set { SetValue(IsExpandedProperty, value); }
    }

    public bool IsTextVisible
    {
        get { return (bool)GetValue(IsTextVisibleProperty); }
        set { SetValue(IsTextVisibleProperty, value); }
    }

    public ObservableCollection<NavigationMenuItem> Items => _items;

    private static void OnIconTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationGroup control)
        {
            control.UpdateIcon();
        }
    }

    private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationGroup control)
        {
            control.UpdateText();
        }
    }

    private static void OnIsExpandedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationGroup control)
        {
            control.UpdateExpandState();
        }
    }

    private static void OnIsTextVisibleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationGroup control)
        {
            control.UpdateTextVisibility();
        }
    }

    private void UpdateIcon()
    {
        GroupIcon.IconType = IconType;
    }

    private void UpdateText()
    {
        GroupTextBlock.Text = Text ?? string.Empty;
    }

    private void UpdateExpandState()
    {
        ItemsContainer.Visibility = IsExpanded ? Visibility.Visible : Visibility.Collapsed;
        
        // Update expand icon
        var template = GroupHeaderButton.Template;
        if (template != null)
        {
            GroupHeaderButton.ApplyTemplate();
            var expandIcon = template.FindName("ExpandIcon", GroupHeaderButton) as IconEx;
            if (expandIcon != null)
            {
                expandIcon.IconType = IsExpanded ? IconType.ChevronUp : IconType.ChevronDown;
            }
        }
    }

    private void UpdateTextVisibility()
    {
        GroupTextBlock.Visibility = IsTextVisible ? Visibility.Visible : Visibility.Collapsed;
        
        // Also update child items text visibility
        foreach (var item in _items)
        {
            item.IsTextVisible = IsTextVisible;
        }
    }

    public void AddItem(NavigationMenuItem item)
    {
        _items.Add(item);
        ItemsContainer.Children.Add(item);
        
        // Set initial text visibility
        item.IsTextVisible = IsTextVisible;
        
        // Add left margin for child items to show hierarchy
        item.Margin = new Thickness(16, 0, 0, 0);
    }

    public void RemoveItem(NavigationMenuItem item)
    {
        _items.Remove(item);
        ItemsContainer.Children.Remove(item);
    }

    public void ClearItems()
    {
        _items.Clear();
        ItemsContainer.Children.Clear();
    }

    private void GroupHeaderButton_Click(object sender, RoutedEventArgs e)
    {
        IsExpanded = !IsExpanded;
    }
}
