using Duckie.Shared.Views.Common;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace Duckie.Views.Controls;

public partial class NavigationGroup : UserControl
{
    public static readonly DependencyProperty IconTypeProperty =
        DependencyProperty.Register(nameof(IconType), typeof(IconType), typeof(NavigationGroup),
            new PropertyMetadata(IconType.ExpandSolid, OnIconTypeChanged));

    public static readonly DependencyProperty TextProperty =
        DependencyProperty.Register(nameof(Text), typeof(string), typeof(NavigationGroup),
            new PropertyMetadata(string.Empty, OnTextChanged));

    public static readonly DependencyProperty IsExpandedProperty =
        DependencyProperty.Register(nameof(IsExpanded), typeof(bool), typeof(NavigationGroup),
            new PropertyMetadata(true, OnIsExpandedChanged));

    public static readonly DependencyProperty IsTextVisibleProperty =
        DependencyProperty.Register(nameof(IsTextVisible), typeof(bool), typeof(NavigationGroup),
            new PropertyMetadata(true, OnIsTextVisibleChanged));

    private readonly ObservableCollection<NavigationMenuItem> _items = new();
    private NavigationHoverCard _hoverCard;
    private Popup _hoverPopup;
    private DispatcherTimer _hoverTimer;
    private bool _isMouseOverGroup = false;
    private bool _isMouseOverCard = false;
    private bool _isPinnedExpanded = false;

    public NavigationGroup()
    {
        InitializeComponent();
        InitializeHoverCard();
        UpdateExpandState();
    }

    public IconType IconType
    {
        get { return (IconType)GetValue(IconTypeProperty); }
        set { SetValue(IconTypeProperty, value); }
    }

    public string Text
    {
        get { return (string)GetValue(TextProperty); }
        set { SetValue(TextProperty, value); }
    }

    public bool IsExpanded
    {
        get { return (bool)GetValue(IsExpandedProperty); }
        set { SetValue(IsExpandedProperty, value); }
    }

    public bool IsTextVisible
    {
        get { return (bool)GetValue(IsTextVisibleProperty); }
        set { SetValue(IsTextVisibleProperty, value); }
    }

    public ObservableCollection<NavigationMenuItem> Items => _items;

    private static void OnIconTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationGroup control)
        {
            control.UpdateIcon();
        }
    }

    private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationGroup control)
        {
            control.UpdateText();
        }
    }

    private static void OnIsExpandedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationGroup control)
        {
            control.UpdateExpandState();
        }
    }

    private static void OnIsTextVisibleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationGroup control)
        {
            control.UpdateTextVisibility();
        }
    }

    private void UpdateIcon()
    {
        GroupIcon.IconType = IconType;
    }

    private void UpdateText()
    {
        GroupTextBlock.Text = Text ?? string.Empty;
    }

    private void UpdateExpandState()
    {
        ItemsContainer.Visibility = IsExpanded ? Visibility.Visible : Visibility.Collapsed;
        
        // Update expand icon
        var template = GroupHeaderButton.Template;
        if (template != null)
        {
            GroupHeaderButton.ApplyTemplate();
            var expandIcon = template.FindName("ExpandIcon", GroupHeaderButton) as IconEx;
            if (expandIcon != null)
            {
                expandIcon.IconType = IsExpanded ? IconType.ChevronUp : IconType.ChevronDown;
            }
        }
    }

    private void InitializeHoverCard()
    {
        _hoverCard = new NavigationHoverCard();
        _hoverCard.CardMouseEnter += (s, e) => _isMouseOverCard = true;
        _hoverCard.CardMouseLeave += (s, e) =>
        {
            _isMouseOverCard = false;
            StartHideTimer();
        };

        _hoverPopup = new Popup
        {
            Child = _hoverCard,
            AllowsTransparency = true,
            PopupAnimation = PopupAnimation.None,
            Placement = PlacementMode.Right,
            PlacementTarget = this,
            HorizontalOffset = 8,
            VerticalOffset = -5,
            StaysOpen = false
        };

        _hoverTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(500) // Slightly longer delay for better UX
        };
        _hoverTimer.Tick += HoverTimer_Tick;
    }

    private void UpdateTextVisibility()
    {
        GroupTextBlock.Visibility = IsTextVisible ? Visibility.Visible : Visibility.Collapsed;

        // Update expand icon and group indicator visibility
        var template = GroupHeaderButton.Template;
        if (template != null)
        {
            GroupHeaderButton.ApplyTemplate();
            var expandIconContainer = template.FindName("ExpandIconContainer", GroupHeaderButton) as Grid;
            var groupIndicator = template.FindName("GroupIndicator", GroupHeaderButton) as Ellipse;

            if (expandIconContainer != null)
                expandIconContainer.Visibility = IsTextVisible ? Visibility.Visible : Visibility.Collapsed;

            if (groupIndicator != null)
                groupIndicator.Visibility = IsTextVisible ? Visibility.Collapsed : Visibility.Visible;
        }

        // Also update child items text visibility
        foreach (var item in _items)
        {
            item.IsTextVisible = IsTextVisible;
        }

        // Update hover card items
        UpdateHoverCardItems();
    }

    private void UpdateHoverCardItems()
    {
        if (_hoverCard == null) return;

        _hoverCard.ClearItems();
        _hoverCard.SetHeaderInfo(Text, IconType);

        foreach (var item in _items)
        {
            _hoverCard.AddItem(item);
        }
    }

    private void StartHideTimer()
    {
        _hoverTimer.Stop();
        _hoverTimer.Start();
    }

    private void HoverTimer_Tick(object sender, EventArgs e)
    {
        _hoverTimer.Stop();

        if (!_isMouseOverGroup && !_isMouseOverCard)
        {
            HideHoverCard();
        }
    }

    private void ShowHoverCard()
    {
        if (!IsTextVisible && _items.Count > 0)
        {
            _hoverTimer.Stop();
            _hoverPopup.IsOpen = true;
            _hoverCard.Show();
        }
    }

    private void HideHoverCard()
    {
        if (_hoverPopup.IsOpen)
        {
            _hoverCard.Hide();
            _hoverPopup.IsOpen = false;
        }
    }

    public void AddItem(NavigationMenuItem item)
    {
        _items.Add(item);
        ItemsContainer.Children.Add(item);

        // Set initial text visibility
        item.IsTextVisible = IsTextVisible;

        // Add left margin for child items to show hierarchy
        item.Margin = new Thickness(20, 0, 0, 0);

        // Make child items visually smaller and less prominent
        item.Height = 32; // Slightly smaller than group header (36px)

        // Update hover card
        UpdateHoverCardItems();
    }

    public void RemoveItem(NavigationMenuItem item)
    {
        _items.Remove(item);
        ItemsContainer.Children.Remove(item);
        UpdateHoverCardItems();
    }

    public void ClearItems()
    {
        _items.Clear();
        ItemsContainer.Children.Clear();
        UpdateHoverCardItems();
    }

    /// <summary>
    /// Force hide hover card (useful when parent window loses focus)
    /// </summary>
    public void ForceHideHoverCard()
    {
        _isMouseOverGroup = false;
        _isMouseOverCard = false;
        _isPinnedExpanded = false;
        HideHoverCard();
    }

    /// <summary>
    /// Update the visual state when sidebar is collapsed/expanded
    /// </summary>
    public void UpdateSidebarState(bool isCollapsed)
    {
        IsTextVisible = !isCollapsed;

        if (isCollapsed && _isPinnedExpanded)
        {
            // Reset pinned state when sidebar collapses
            _isPinnedExpanded = false;
            HideHoverCard();
        }
    }

    private void GroupHeaderButton_Click(object sender, RoutedEventArgs e)
    {
        if (IsTextVisible)
        {
            // Normal expand/collapse behavior when text is visible
            IsExpanded = !IsExpanded;
            _isPinnedExpanded = IsExpanded;
        }
        else
        {
            // In icon mode, clicking toggles pinned expansion
            _isPinnedExpanded = !_isPinnedExpanded;
            if (_isPinnedExpanded)
            {
                ShowHoverCard();
            }
            else
            {
                HideHoverCard();
            }
        }
    }

    private void GroupHeaderButton_MouseEnter(object sender, MouseEventArgs e)
    {
        _isMouseOverGroup = true;

        if (!IsTextVisible && !_isPinnedExpanded)
        {
            ShowHoverCard();
        }
    }

    private void GroupHeaderButton_MouseLeave(object sender, MouseEventArgs e)
    {
        _isMouseOverGroup = false;

        if (!IsTextVisible && !_isPinnedExpanded)
        {
            StartHideTimer();
        }
    }
}
