<UserControl x:Class="Duckie.Views.Controls.NavigationGroup"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="200">

    <StackPanel>
        <!-- Group Header -->
        <Button x:Name="GroupHeaderButton"
                HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                Padding="0" Margin="0,4,0,0" Height="36" Cursor="Hand"
                Click="GroupHeaderButton_Click"
                MouseEnter="GroupHeaderButton_MouseEnter"
                MouseLeave="GroupHeaderButton_MouseLeave">
            <Button.Style>
                <Style TargetType="Button">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="Button">
                                <Grid x:Name="BackgroundGrid" Background="{TemplateBinding Background}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="12"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Left spacing -->
                                    <Grid Grid.Column="0"/>

                                    <!-- Content area -->
                                    <Grid Grid.Column="1" Margin="8,0,0,0">
                                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="Center"/>
                                    </Grid>

                                    <!-- Expand/Collapse indicator (visible when text is shown) -->
                                    <Grid Grid.Column="2" Margin="10,0,12,0" x:Name="ExpandIconContainer">
                                        <common:IconEx x:Name="ExpandIcon" IconType="ArrowCounterClockwise" Width="12" Height="12" Foreground="#6C757D"/>
                                    </Grid>

                                    <!-- Group indicator dot (visible when text is hidden) -->
                                    <Ellipse x:Name="GroupIndicator" Grid.Column="2"
                                             Width="6" Height="6" Fill="#007BFF"
                                             Margin="0,0,15,0" Visibility="Collapsed"/>
                                </Grid>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="BackgroundGrid" Property="Background" Value="#F8F9FA"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter TargetName="BackgroundGrid" Property="Background" Value="#E9ECEF"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Button.Style>

            <StackPanel Orientation="Horizontal">
                <common:IconEx x:Name="GroupIcon" Margin="0,0,8,0" Width="18" Height="18" Foreground="#007BFF"/>
                <TextBlock x:Name="GroupTextBlock" VerticalAlignment="Center"
                           Foreground="#495057" FontWeight="SemiBold" FontSize="13"/>
            </StackPanel>
        </Button>

        <!-- Group Items Container -->
        <StackPanel x:Name="ItemsContainer" Margin="0,2,0,0">
            <!-- Child navigation items will be added here programmatically -->
        </StackPanel>
    </StackPanel>
</UserControl>
