# Duckie - 多功能系统工具

[English](./README.md)

一个专业的 Windows (WPF) 应用程序，集成图像处理、PAC 代理管理、系统音量控制等系统实用工具。

---

## ✨ 核心功能

### 🖼️ 图像处理
- **基础编辑**: 90° 旋转、水平/垂直翻转，操作简单直观
- **缩放预览**: 10%-300% 缩放控制，支持鼠标滚轮缩放和适应窗口
- **拖拽支持**: 智能拖拽文件加载，支持 PNG、JPG、BMP、GIF 等常见格式
- **导出功能**: 支持图像导出和 ICO 图标生成

### 🌐 PAC 代理管理
- **配置管理**: 添加、编辑、删除 PAC 配置，支持快速切换
- **状态指示**: 清晰的视觉状态指示，托盘图标徽章显示
- **注册表集成**: 自动管理 Windows 系统代理设置
- **直连模式**: 一键禁用代理，支持系统托盘快速操作

### 🔊 系统音量控制
- **全局热键**: Alt+Shift+8/9/0 控制音量增减和静音
- **悬浮提示**: 音量变化时显示现代化悬浮窗，自动淡出
- **图标指示**: 根据音量状态智能切换图标显示

### 🛠️ 系统实用工具
- **APP 显隐**: 基于系统托盘的窗口显示/隐藏
- **APP 退出**: 干净的应用程序终止
- **调小音量**: 精细音量控制
- **调大音量**: 精细音量控制  
- **静音/取消静音**: 快速静音切换
- **图像转 ICO**: 将图像转换为 ICO 格式
- **在线更新**: 自动更新检查（仅桌面版）
- **代理配置**: 网络代理配置管理

### ⚙️ 系统集成
- **系统托盘**: 后台运行，支持左键显示/隐藏主窗口
- **开机启动**: 可选开机自启动，无缝集成系统
- **热键配置**: 自定义全局热键，支持组合键设置
- **现代界面**: 简洁专业的 WPF 界面，支持侧边栏折叠

## 🚀 快速使用

### 图像处理
1. 拖拽图片到窗口或点击"打开"按钮加载图像
2. 使用工具栏按钮进行旋转、翻转操作
3. 鼠标滚轮 + Ctrl 键缩放，或使用缩放控制条
4. 点击"保存"或"导出ICO"保存处理结果

### PAC 代理管理
1. 点击"Add PAC"添加代理配置（名称 + PAC URL）
2. 点击配置卡片应用代理，点击"No PAC"禁用代理
3. 使用"Edit"/"Delete"按钮管理现有配置
4. 系统托盘右键菜单可快速切换代理

### 音量控制
- **Alt + Shift + 8**: 音量减小
- **Alt + Shift + 9**: 音量增大  
- **Alt + Shift + 0**: 静音/取消静音

## 📋 平台兼容性

| 功能 | 功能UI | 中键菜单 | 右键菜单 | 状态显示 | MS Store 可用 |
|------|--------|----------|----------|----------|---------------|
| APP 显示/隐藏 | ✗ | ✓ | ✓ | ✗ | ✓ |
| APP 退出 | ✗ | ✓ | ✓ | ✗ | ✓ |
| 调小音量 | ✗ | ✓ | ✗ | ✗ | ✓ |
| 调大音量 | ✗ | ✓ | ✗ | ✗ | ✓ |
| 静音/取消静音 | ✗ | ✓ | ✗ | ✗ | ✓ |
| 图像转 ICO | ✓ | ✗ | ✗ | ✗ | ✓ |
| PAC 切换 | ✓ | ✓ | ✓ | ✓ | ✗ |
| 网络驱动版本 | ✗ | ✓ | ✗ | ✗ | ✗ |
| 代理配置 | ✓ | ✗ | ✗ | ✗ | ✗ |

## 💡 技术亮点

- **内存优化**: 智能资源管理，图像处理后及时释放内存
- **异步操作**: 非阻塞用户界面，响应流畅
- **错误处理**: 完善的异常处理和用户友好的错误提示
- **模块化架构**: 清晰的服务分层，便于功能扩展
- **跨平台就绪**: 为桌面版和 Microsoft Store 部署设计
- **用户友好**：直观的界面设计，清晰的视觉反馈
- **专业级**：企业就绪，具备强大的错误处理能力


