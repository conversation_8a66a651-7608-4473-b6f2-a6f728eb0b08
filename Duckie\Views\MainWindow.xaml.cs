﻿using <PERSON>ie.Shared;
using <PERSON><PERSON>.Shared.Utils.Localization;
using Duckie.Shared.Views;
using Duckie.Views.Controls;
using System.ComponentModel;
using System.Windows;
using System.Windows.Media;
using System.Windows.Shapes;

namespace <PERSON>ie;

public partial class MainWindow : Window, IMainWindow
{
    private bool _isSidebarCollapsed = false;
    private const double CollapsedWidth = 54;
    private const double ExpandedWidth = 120;

    // Navigation items
    private NavigationMenuItem _navImageProcessing;
    private NavigationMenuItem _navPacManagement;
    private NavigationMenuItem _navVolumeControl;
    private NavigationMenuItem _navHotkeyManagement;
    private NavigationMenuItem _navSettings;
    private NavigationMenuItem _navAbout;

    public MainWindow()
    {
        AppEnv.MainWindow = this;

        InitializeComponent();

        InitializeNavigation();
        UpdateNavigationState();
        ShowImageProcessing();

        // Handle window deactivation to hide hover cards
        Deactivated += MainWindow_Deactivated;
    }

    private void MainWindow_Deactivated(object sender, EventArgs e)
    {
        // Hide all hover cards when window loses focus
        NavGroupMedia.ForceHideHoverCard();
        NavGroupNetwork.ForceHideHoverCard();
        NavGroupSystem.ForceHideHoverCard();
    }

    public void Toggle()
    {
        if (Visibility == Visibility.Visible)
        {
            Hide();
        }
        else
        {
            Show();
            Activate();
        }
    }

    /// <summary>
    /// Initialize navigation groups and items
    /// </summary>
    private void InitializeNavigation()
    {
        // Create navigation items
        _navImageProcessing = new NavigationMenuItem
        {
            IconType = Shared.Views.Common.IconType.Image,
            Text = LocKey.Nav_Image.Text(),
        };
        _navImageProcessing.Click += MenuImageProcessing_Click;

        _navPacManagement = new NavigationMenuItem
        {
            IconType = Shared.Views.Common.IconType.Network,
            Text = LocKey.Nav_PAC.Text(),
        };
        _navPacManagement.Click += MenuPacManagement_Click;

        _navVolumeControl = new NavigationMenuItem
        {
            IconType = Shared.Views.Common.IconType.SpeakerHigh,
            Text = LocKey.Nav_VolumeControl.Text(),
        };
        _navVolumeControl.Click += MenuVolumeControl_Click;

        _navHotkeyManagement = new NavigationMenuItem
        {
            IconType = Shared.Views.Common.IconType.SettingLine,
            Text = LocKey.Nav_HotkeyManagement.Text(),
        };
        _navHotkeyManagement.Click += MenuHotkeyManagement_Click;

        _navSettings = new NavigationMenuItem
        {
            IconType = Shared.Views.Common.IconType.SettingLine,
            Text = LocKey.Nav_Settings.Text(),
        };
        _navSettings.Click += MenuSettings_Click;

        _navAbout = new NavigationMenuItem
        {
            IconType = Shared.Views.Common.IconType.InformationLine,
            Text = LocKey.Nav_About.Text(),
        };
        _navAbout.Click += MenuAbout_Click;

        // Add items to groups
        NavGroupMedia.AddItem(_navImageProcessing);
        NavGroupNetwork.AddItem(_navPacManagement);
        NavGroupSystem.AddItem(_navVolumeControl);
        NavGroupSystem.AddItem(_navHotkeyManagement);

        // Add standalone items (Settings and About) to the bottom
        BottomNavigationPanel.Children.Add(_navSettings);
        BottomNavigationPanel.Children.Add(_navAbout);
    }

    /// <summary>
    /// Toggle sidebar collapse/expand
    /// </summary>
    private void ToggleButton_Click(object sender, RoutedEventArgs e)
    {
        _isSidebarCollapsed = !_isSidebarCollapsed;

        var targetWidth = _isSidebarCollapsed ? CollapsedWidth : ExpandedWidth;

        // Simple width change without complex animation
        SidebarColumn.Width = new GridLength(targetWidth);

        // Handle text visibility for groups and individual items
        NavGroupMedia.UpdateSidebarState(_isSidebarCollapsed);
        NavGroupNetwork.UpdateSidebarState(_isSidebarCollapsed);
        NavGroupSystem.UpdateSidebarState(_isSidebarCollapsed);
        _navSettings.IsTextVisible = !_isSidebarCollapsed;
        _navAbout.IsTextVisible = !_isSidebarCollapsed;

        // Update toggle icon
        UpdateToggleIcon();
    }

    /// <summary>
    /// Update toggle button icon based on sidebar state
    /// </summary>
    private void UpdateToggleIcon()
    {
        try
        {
            var toggleIcon = FindName("ToggleIcon") as Path;
            if (toggleIcon != null)
            {
                var iconData = _isSidebarCollapsed
                    ? "M9,5 L15,12 L9,19" // Right arrow (expand)
                    : "M15,5 L9,12 L15,19"; // Left arrow (collapse)

                toggleIcon.Data = Geometry.Parse(iconData);
            }
        }
        catch
        {
            // Ignore icon update errors
        }
    }

    /// <summary>
    /// Switch to image processing function
    /// </summary>
    private void MenuImageProcessing_Click(object sender, RoutedEventArgs e)
    {
        ShowImageProcessing();
    }

    /// <summary>
    /// Switch to PAC management function
    /// </summary>
    private void MenuPacManagement_Click(object sender, RoutedEventArgs e)
    {
        ShowPacManagement();
    }

    /// <summary>
    /// Show volume control page
    /// </summary>
    private void MenuVolumeControl_Click(object sender, RoutedEventArgs e)
    {
        ShowVolumeControl();
    }

    /// <summary>
    /// Show hotkey management page
    /// </summary>
    private void MenuHotkeyManagement_Click(object sender, RoutedEventArgs e)
    {
        ShowHotkeyManagement();
    }

    /// <summary>
    /// Show about information
    /// </summary>
    private void MenuAbout_Click(object sender, RoutedEventArgs e)
    {
        ShowAbout();
    }

    /// <summary>
    /// Show settings page
    /// </summary>
    private void MenuSettings_Click(object sender, RoutedEventArgs e)
    {
        ShowSettings();
    }

    /// <summary>
    /// Update navigation button states with visual feedback
    /// </summary>
    private void UpdateNavigationState()
    {
        // Reset all navigation buttons
        _navImageProcessing.IsSelected = false;
        _navPacManagement.IsSelected = false;
        _navVolumeControl.IsSelected = false;
        _navHotkeyManagement.IsSelected = false;
        _navSettings.IsSelected = false;
        _navAbout.IsSelected = false;
    }



    /// <summary>
    /// Show image processing interface
    /// </summary>
    private void ShowImageProcessing()
    {
        ImageViewControl.Visibility = Visibility.Visible;
        PacManageViewControl.Visibility = Visibility.Collapsed;
        VolumeControlViewControl.Visibility = Visibility.Collapsed;
        HotkeyManagementViewControl.Visibility = Visibility.Collapsed;
        SettingsViewControl.Visibility = Visibility.Collapsed;
        AboutViewControl.Visibility = Visibility.Collapsed;

        // Update navigation state
        UpdateNavigationState();
        _navImageProcessing.IsSelected = true;

        Title = LocKey.Title_Image.Text();
    }

    private void ShowPacManagement()
    {
        ImageViewControl.Visibility = Visibility.Collapsed;
        PacManageViewControl.Visibility = Visibility.Visible;
        VolumeControlViewControl.Visibility = Visibility.Collapsed;
        HotkeyManagementViewControl.Visibility = Visibility.Collapsed;
        SettingsViewControl.Visibility = Visibility.Collapsed;
        AboutViewControl.Visibility = Visibility.Collapsed;

        // Update navigation state
        UpdateNavigationState();
        _navPacManagement.IsSelected = true;

        Title = LocKey.Title_PAC.Text();
    }

    private void ShowSettings()
    {
        ImageViewControl.Visibility = Visibility.Collapsed;
        PacManageViewControl.Visibility = Visibility.Collapsed;
        VolumeControlViewControl.Visibility = Visibility.Collapsed;
        HotkeyManagementViewControl.Visibility = Visibility.Collapsed;
        SettingsViewControl.Visibility = Visibility.Visible;
        AboutViewControl.Visibility = Visibility.Collapsed;

        // Update navigation state
        UpdateNavigationState();
        _navSettings.IsSelected = true;

        Title = LocKey.Title_Settings.Text();
    }

    private void ShowVolumeControl()
    {
        ImageViewControl.Visibility = Visibility.Collapsed;
        PacManageViewControl.Visibility = Visibility.Collapsed;
        VolumeControlViewControl.Visibility = Visibility.Visible;
        HotkeyManagementViewControl.Visibility = Visibility.Collapsed;
        SettingsViewControl.Visibility = Visibility.Collapsed;
        AboutViewControl.Visibility = Visibility.Collapsed;

        // Update navigation state
        UpdateNavigationState();
        _navVolumeControl.IsSelected = true;

        Title = LocKey.Title_VolumeControl.Text();
    }

    private void ShowHotkeyManagement()
    {
        ImageViewControl.Visibility = Visibility.Collapsed;
        PacManageViewControl.Visibility = Visibility.Collapsed;
        VolumeControlViewControl.Visibility = Visibility.Collapsed;
        HotkeyManagementViewControl.Visibility = Visibility.Visible;
        SettingsViewControl.Visibility = Visibility.Collapsed;
        AboutViewControl.Visibility = Visibility.Collapsed;

        // Update navigation state
        UpdateNavigationState();
        _navHotkeyManagement.IsSelected = true;

        Title = LocKey.Title_HotkeyManagement.Text();
    }

    private void ShowAbout()
    {
        ImageViewControl.Visibility = Visibility.Collapsed;
        PacManageViewControl.Visibility = Visibility.Collapsed;
        VolumeControlViewControl.Visibility = Visibility.Collapsed;
        HotkeyManagementViewControl.Visibility = Visibility.Collapsed;
        SettingsViewControl.Visibility = Visibility.Collapsed;
        AboutViewControl.Visibility = Visibility.Visible;

        // Update navigation state
        UpdateNavigationState();
        _navAbout.IsSelected = true;

        Title = LocKey.Title_About.Text();
    }
}