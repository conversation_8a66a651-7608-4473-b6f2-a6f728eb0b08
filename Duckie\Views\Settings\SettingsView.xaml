<UserControl x:Class="Duckie.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
             xmlns:loc="clr-namespace:Duckie.Shared.Utils.Localization;assembly=Duckie.Shared"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             Background="White">
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <DockPanel Margin="0 30" MaxWidth="600" HorizontalAlignment="Center">
            <StackPanel DockPanel.Dock="Top">
                <!-- Settings Title -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,30">
                    <common:TextEx Text="{loc:Localize Nav_Settings}" FontSize="32" FontWeight="Bold" Foreground="#333" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Language Settings Section -->
                <StackPanel Margin="0,0,0,30">
                    <common:TextEx Text="{loc:Localize Settings_Language}" FontSize="18" FontWeight="SemiBold" Foreground="#333" Margin="0,0,0,16"/>
                    
                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1"
                            CornerRadius="6" Padding="16" MaxWidth="400">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Language Icon -->
                            <common:IconEx IconType="Earth"/>

                            <!-- Language Label -->
                            <common:TextEx Grid.Column="1" Text="{loc:Localize Nav_Language}" Margin="8 0 10 0"
                                           FontSize="14" Foreground="#333" VerticalAlignment="Center"/>

                            <!-- Language ComboBox -->
                            <ComboBox x:Name="LanguageComboBox" Grid.Column="2"
                                      Width="120" Height="28"
                                      FontSize="12" VerticalContentAlignment="Center"
                                      SelectionChanged="LanguageComboBox_SelectionChanged"
                                      ToolTip="{loc:Localize Nav_Language}">
                                <ComboBox.Style>
                                    <Style TargetType="ComboBox">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="ComboBox">
                                                    <Grid>
                                                        <ToggleButton x:Name="ToggleButton"
                                                                      Background="White"
                                                                      BorderThickness="1" BorderBrush="#CCC"
                                                                      IsChecked="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                                                      ClickMode="Press">
                                                            <ToggleButton.Style>
                                                                <Style TargetType="ToggleButton">
                                                                    <Setter Property="Template">
                                                                        <Setter.Value>
                                                                            <ControlTemplate TargetType="ToggleButton">
                                                                                <Border x:Name="border" Background="{TemplateBinding Background}"
                                                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                                                        CornerRadius="4" Padding="8,6">
                                                                                    <Grid>
                                                                                        <Grid.ColumnDefinitions>
                                                                                            <ColumnDefinition Width="*"/>
                                                                                            <ColumnDefinition Width="Auto"/>
                                                                                        </Grid.ColumnDefinitions>
                                                                                        <TextBlock x:Name="LanguageText" Grid.Column="0" Text="English"
                                                                                                   FontSize="12" Foreground="#333"
                                                                                                   VerticalAlignment="Center" HorizontalAlignment="Left"/>
                                                                                        <Path Grid.Column="1" Data="M7 10l5 5 5-5z"
                                                                                              Fill="#666" Stretch="Uniform"
                                                                                              Width="10" Height="10"
                                                                                              VerticalAlignment="Center" Margin="8,0,0,0"/>
                                                                                    </Grid>
                                                                                </Border>
                                                                                <ControlTemplate.Triggers>
                                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                                        <Setter TargetName="border" Property="BorderBrush" Value="#007ACC"/>
                                                                                    </Trigger>
                                                                                    <Trigger Property="IsPressed" Value="True">
                                                                                        <Setter TargetName="border" Property="Background" Value="#F0F0F0"/>
                                                                                    </Trigger>
                                                                                </ControlTemplate.Triggers>
                                                                            </ControlTemplate>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Style>
                                                            </ToggleButton.Style>
                                                        </ToggleButton>
                                                        <ContentPresenter x:Name="ContentSite" IsHitTestVisible="False"
                                                                          Content="{TemplateBinding SelectionBoxItem}"
                                                                          ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                                          ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                                          Margin="8,6,28,6" VerticalAlignment="Center"
                                                                          HorizontalAlignment="Left" Visibility="Collapsed"/>
                                                        <Popup x:Name="Popup" Placement="Bottom" IsOpen="{TemplateBinding IsDropDownOpen}"
                                                               AllowsTransparency="True" Focusable="False" PopupAnimation="Slide">
                                                            <Grid x:Name="DropDown" SnapsToDevicePixels="True"
                                                                  MinWidth="{TemplateBinding ActualWidth}" MaxHeight="200">
                                                                <Border x:Name="DropDownBorder" Background="White"
                                                                        BorderThickness="1" BorderBrush="#CCC" CornerRadius="4">
                                                                    <Border.Effect>
                                                                        <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="4"/>
                                                                    </Border.Effect>
                                                                    <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                                                        <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
                                                                    </ScrollViewer>
                                                                </Border>
                                                            </Grid>
                                                        </Popup>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ComboBox.Style>
                                <ComboBoxItem Content="English" Tag="en-US"/>
                                <ComboBoxItem Content="简体中文" Tag="zh-CN"/>
                            </ComboBox>
                        </Grid>
                    </Border>
                </StackPanel>

                <!-- Hotkeys Settings Section -->
                <StackPanel Margin="0,0,0,20">
                    <common:TextEx Text="{loc:Localize Settings_HotKeys}" FontSize="18" FontWeight="SemiBold" Foreground="#333" Margin="0,0,0,16"/>
                    
                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1"
                            CornerRadius="6" Padding="16">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <ItemsControl x:Name="HotKeysItemsControl">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1" 
                                                CornerRadius="4" Margin="0,0,0,8" Padding="12">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="200"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <!-- Service Name -->
                                                <common:TextEx Grid.Column="0" Text="{Binding Name}" 
                                                               FontSize="14" Foreground="#333" 
                                                               VerticalAlignment="Center" Margin="0,0,12,0"/>
                                                
                                                <!-- Hotkey Input -->
                                                <TextBox Grid.Column="1" x:Name="HotKeyTextBox"
                                                         Text="{Binding HotKeyDisplay, Mode=TwoWay}"
                                                         IsReadOnly="True" 
                                                         Background="White" BorderBrush="#CCC" BorderThickness="1"
                                                         Padding="8,6" VerticalContentAlignment="Center"
                                                         FontSize="12" Foreground="#333"
                                                         PreviewKeyDown="HotKeyTextBox_PreviewKeyDown"
                                                         GotFocus="HotKeyTextBox_GotFocus"
                                                         LostFocus="HotKeyTextBox_LostFocus"
                                                         Tag="{Binding}"
                                                         ToolTip="{loc:Localize Key=Settings_HotKey_Tooltip}"/>
                                                
                                                <!-- Clear Button -->
                                                <common:IconButton Grid.Column="2" Click="ClearButton_Click"
                                                                   Tag="{Binding}"
                                                                   IconType="Delete"
                                                                   Margin="8 0 0 0"
                                                                   ToolTip="{loc:Localize Key=Settings_HotKey_Clear}"
                                                                   IsEnabled="{Binding HasHotKey}" />
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Border>
                </StackPanel>
            </StackPanel>
        </DockPanel>
    </ScrollViewer>
</UserControl>
