<UserControl x:Class="Duckie.Shared.Views.Common.IconEx"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="Transparent">
    <Path x:Name="IconPath" 
          Stretch="Uniform"
          Width="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=IconSize}"
          Height="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=IconSize}"/>
</UserControl>
