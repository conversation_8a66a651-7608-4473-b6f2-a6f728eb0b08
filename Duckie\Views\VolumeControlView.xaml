<UserControl x:Class="Duckie.Views.VolumeControlView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
             xmlns:loc="clr-namespace:Duckie.Shared.Utils.Localization;assembly=Duckie.Shared"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="20">
            <!-- Header -->
            <TextBlock Text="{loc:Localize Nav_VolumeControl}" 
                       FontSize="24" FontWeight="Bold" 
                       Foreground="#333" Margin="0,0,0,20"/>
            
            <!-- Volume Control Section -->
            <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1" 
                    CornerRadius="8" Padding="20" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="音量控制" FontSize="16" FontWeight="SemiBold" 
                               Foreground="#495057" Margin="0,0,0,15"/>
                    
                    <!-- Current Volume Display -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <common:IconEx Grid.Column="0" IconType="SpeakerHigh" 
                                       Width="20" Height="20" Margin="0,0,10,0"/>
                        <ProgressBar Grid.Column="1" x:Name="VolumeProgressBar" 
                                     Height="8" Minimum="0" Maximum="100" Value="50"/>
                        <TextBlock Grid.Column="2" x:Name="VolumePercentText" 
                                   Text="50%" Margin="10,0,0,0" VerticalAlignment="Center"/>
                    </Grid>
                    
                    <!-- Volume Control Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                        <Button Content="音量减" Style="{StaticResource ModernButtonStyle}" 
                                Margin="0,0,10,0" Padding="15,8" Click="VolumeDown_Click"/>
                        <Button Content="静音" Style="{StaticResource ModernButtonStyle}" 
                                Margin="0,0,10,0" Padding="15,8" Click="ToggleMute_Click"/>
                        <Button Content="音量加" Style="{StaticResource ModernButtonStyle}" 
                                Padding="15,8" Click="VolumeUp_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <!-- Hotkey Settings Section -->
            <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1" 
                    CornerRadius="8" Padding="20" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="热键设置" FontSize="16" FontWeight="SemiBold" 
                               Foreground="#495057" Margin="0,0,0,15"/>
                    
                    <!-- Hotkey List -->
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Volume Down -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="音量减" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <Border Grid.Row="0" Grid.Column="1" Background="#F8F9FA" 
                                BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="4" 
                                Padding="8,4" Margin="10,5">
                            <TextBlock Text="Alt + Shift + 8" FontFamily="Consolas" FontSize="12"/>
                        </Border>
                        
                        <!-- Volume Up -->
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="音量加" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <Border Grid.Row="1" Grid.Column="1" Background="#F8F9FA" 
                                BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="4" 
                                Padding="8,4" Margin="10,5">
                            <TextBlock Text="Alt + Shift + 9" FontFamily="Consolas" FontSize="12"/>
                        </Border>
                        
                        <!-- Mute Toggle -->
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="静音切换" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <Border Grid.Row="2" Grid.Column="1" Background="#F8F9FA" 
                                BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="4" 
                                Padding="8,4" Margin="10,5">
                            <TextBlock Text="Alt + Shift + 0" FontFamily="Consolas" FontSize="12"/>
                        </Border>
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- Volume Overlay Settings -->
            <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1" 
                    CornerRadius="8" Padding="20">
                <StackPanel>
                    <TextBlock Text="悬浮提示设置" FontSize="16" FontWeight="SemiBold" 
                               Foreground="#495057" Margin="0,0,0,15"/>
                    
                    <CheckBox x:Name="ShowOverlayCheckBox" Content="显示音量变化悬浮提示" 
                              IsChecked="True" Margin="0,5"/>
                    <CheckBox x:Name="AutoHideOverlayCheckBox" Content="自动隐藏悬浮提示" 
                              IsChecked="True" Margin="0,5"/>
                    
                    <Button Content="测试悬浮提示" Style="{StaticResource ModernButtonStyle}" 
                            HorizontalAlignment="Left" Margin="0,15,0,0" Padding="15,8" 
                            Click="TestOverlay_Click"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
