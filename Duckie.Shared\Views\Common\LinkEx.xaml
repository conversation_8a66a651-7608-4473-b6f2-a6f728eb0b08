<UserControl x:Class="Duckie.Shared.Views.Common.LinkEx"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common">
    <StackPanel Orientation="Horizontal">
        <common:IconEx x:Name="LinkIcon" IconSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
        <TextBlock VerticalAlignment="Center">
            <Hyperlink x:Name="LinkHyperlink" RequestNavigate="LinkHyperlink_RequestNavigate"
                       Foreground="{StaticResource PrimaryTextBrush}" TextDecorations="Underline">
                <TextBlock x:Name="LinkText" FontSize="14"/>
            </Hyperlink>
        </TextBlock>
    </StackPanel>
</UserControl>
