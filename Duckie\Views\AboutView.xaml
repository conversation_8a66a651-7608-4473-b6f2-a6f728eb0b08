<UserControl x:Class="Duckie.Views.AboutView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:loc="clr-namespace:Duckie.Shared.Utils.Localization;assembly=Duckie.Shared"
             xmlns:common="clr-namespace:Duckie.Shared.Views.Common;assembly=Duckie.Shared"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             Background="White">
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <DockPanel Margin="0 30" MaxWidth="600" HorizontalAlignment="Center">
            <StackPanel DockPanel.Dock="Top">
                <!-- App Title -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,20">
                    <common:TextEx Text="{loc:Localize AppTitle}" FontSize="32" FontWeight="Bold" Foreground="#333" HorizontalAlignment="Center"/>
                    <common:TextEx x:Name="versionText" FontSize="14" Foreground="#666" Margin="0,4,0,0" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- App Info Section -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,40">
                    <common:TextEx TextAlignment="Center" FontSize="14" Text="{loc:Localize AppDescription}"/>
                    <common:TextEx TextAlignment="Center" FontSize="14" Margin="0 8 0 0" Text="{loc:Localize AppSubtitle}"/>

                    <!-- Runtime Environment Info -->
                    <Border x:Name="RuntimeInfoBorder" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                        CornerRadius="4" Padding="12,8" Margin="0,16,0,0" Visibility="Collapsed">
                        <StackPanel>
                            <common:TextEx x:Name="RuntimeTitle" FontSize="12" FontWeight="SemiBold" Foreground="#495057" HorizontalAlignment="Center"/>
                            <common:TextEx x:Name="RuntimeDescription" FontSize="11" Foreground="#6C757D" Margin="0,2,0,0" 
                                           HorizontalAlignment="Center" TextWrapping="Wrap" TextAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </StackPanel>

            <!-- Links Section -->
            <StackPanel Orientation="Horizontal" VerticalAlignment="Bottom" DockPanel.Dock="Bottom">
                <common:LinkEx IconType="Globe" NavigateUri="https://cg-zhou.top"
                               Text="Duckie" VerticalAlignment="Center" Margin="10 0"/>

                <common:LinkEx IconType="MicrosoftStore" NavigateUri="https://apps.microsoft.com/detail/9msz8m2wxzc4"
                              Text="Microsoft Store" VerticalAlignment="Center" Margin="10 0"/>

                <common:LinkEx IconType="Github" NavigateUri="https://github.com/cg-zhou/duckie"
                               Text="GitHub" VerticalAlignment="Center" Margin="10 0"/>

                <common:LinkEx IconType="Email" NavigateUri="mailto:<EMAIL>"
                               Text="Email" VerticalAlignment="Center" Margin="10 0"/>
            </StackPanel>
        </DockPanel>
    </ScrollViewer>
</UserControl>
