﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- 主题色 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#0069BA"/>
    <SolidColorBrush x:Key="SelectedBrush" Color="#EAEAEA"/>
    <SolidColorBrush x:Key="HoverBrush" Color="#EDEDED"/>
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="#333"/>
    <SolidColorBrush x:Key="IconBrush" Color="#333"/>
    <SolidColorBrush x:Key="DisabledForegroundBrush" Color="#BBB"/>

    <Style TargetType="TextBox">
        <Setter Property="Padding" Value="3 2"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>
    <Style TargetType="PasswordBox">
        <Setter Property="Padding" Value="3 2"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <Style TargetType="Button">
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="Foreground" Value="#333"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            BorderThickness="1"
                            BorderBrush="#CCC"
                            Padding="12 6"
                            CornerRadius="4"
                            Background="#F5F5F5">
                        <ContentPresenter HorizontalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#E0E0E0"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#AAA"/>
                            <Setter TargetName="border" Property="Cursor" Value="Hand"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#D0D0D0"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#999"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="#F8F8F8"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#DDD"/>
                            <Setter Property="Foreground" Value="#AAA"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Grid" x:Key="horizontalLine">
        <Setter Property="Background" Value="Gray"/>
        <Setter Property="Height" Value="1"/>
        <Setter Property="Margin" Value="0 5"/>
    </Style>

    <Style x:Key="row" TargetType="DockPanel">
        <Setter Property="Margin" Value="0 0 0 10"/>
    </Style>

    <Style x:Key="label" TargetType="TextBlock">
        <Setter Property="DockPanel.Dock" Value="Left"/>
        <Setter Property="MinWidth" Value="70"/>
        <Setter Property="Margin" Value="0 0 10 0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Modern Button Styles for ImageView -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DEE2E6"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="10,4"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4" Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#F8F9FA"/>
                            <Setter Property="BorderBrush" Value="#ADB5BD"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#E9ECEF"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{StaticResource DisabledForegroundBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="#007BFF"/>
        <Setter Property="BorderBrush" Value="#007BFF"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4" Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#0056B3"/>
                            <Setter Property="BorderBrush" Value="#0056B3"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#004085"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
